# Product Context: Kids Reading Manager

## Why this project exists
The Kids Reading Manager is a mobile-friendly web application designed to help teachers, teaching assistants, or volunteers track reading sessions with primary school children. It provides a simple and efficient way to record and monitor reading progress for up to 30 children.

## What problems it solves
- Tracking which children have been read with recently and which need attention
- Recording assessment levels for each reading session (struggling, needs help, independent)
- Providing visual indicators to quickly identify children who need reading support
- Enabling efficient data entry during limited volunteer time
- Maintaining persistent records of reading sessions across different devices
- Generating statistics and reports for teachers

## How it should work
1. **Student Management**:
   - Add students individually or via bulk import
   - View student cards with reading status indicators
   - Edit or delete students as needed

2. **Reading Session Recording**:
   - Standard mode for detailed entries
   - Quick Entry mode for rapid logging
   - Record date, assessment level, and optional notes

3. **Statistics and Reporting**:
   - View overview dashboard with key metrics
   - Identify students who need attention
   - Track reading frequency per student
   - Export data for sharing with teachers

4. **Data Storage**:
   - Local storage for data persistence
   - API backend for data management
   - Export/import functionality for backup and sharing