{"name": "kids-reading-manager-cloudflare", "version": "1.14.0", "description": "Cloudflare Workers implementation of Kids Reading Manager API", "main": "src/index.js", "scripts": {"start": "rsbuild dev", "start:server": "node server/index.js", "start:dev": "npm run start:server & npm run start", "start:prod": "npm run build && npm run start:server", "dev": "wrangler dev", "deploy": "wrangler deploy", "migrate": "node scripts/migration.js", "build": "rsbuild build", "build:deploy": "./scripts/build-and-deploy.sh", "build:deploy:dev": "./scripts/build-and-deploy.sh dev"}, "author": "", "license": "CC BY-NC 4.0", "dependencies": {"@anthropic-ai/sdk": "^0.61.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "7.0.2", "@mui/material": "7.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^4.21.2", "hono": "^4.7.7", "qr-scanner": "^1.4.2", "react": "19.1.0", "react-dom": "19.1.0", "uuid": "11.1.0"}, "devDependencies": {"@rsbuild/core": "^1.3.9", "@rsbuild/plugin-react": "^1.2.0", "wrangler": "^4.12.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"esbuild": "0.25.2"}}