require('dotenv').config();
const Anthropic = require('@anthropic-ai/sdk');

let express, bodyParser, cors;
try {
  express = require('express');
  bodyParser = require('body-parser');
  cors = require('cors');
} catch (err) {
  // Log detailed diagnostics to help identify issues when require() fails
  console.error('Error requiring server dependencies (express/body-parser/cors).');
  console.error('Error stack:', err && err.stack ? err.stack : err);
  console.error('Node version:', process.version);
  console.error('Working directory:', process.cwd());
  try {
    const pkg = require('../package.json');
    console.error('package.json dependencies:', pkg.dependencies);
  } catch (pkgErr) {
    console.error('Failed to read package.json:', pkgErr && pkgErr.stack ? pkgErr.stack : pkgErr);
  }
  console.error('Environment variables (first 50 keys):', Object.keys(process.env).slice(0,50));
  // Exit with non-zero code so the caller sees a failure
  process.exit(1);
}
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000; // Use port 3000 for the combined server
// For local development use a project-local config directory so we don't require root permissions.
// In production (Docker) you can mount /config if desired.
const DATA_FILE = path.join(__dirname, '..', 'config', 'app_data.json');

// Ensure config directory exists (Docker volume mount handles host side)
const configDir = path.dirname(DATA_FILE); // Should be '/config'
if (!fs.existsSync(configDir)) {
  try {
    fs.mkdirSync(configDir, { recursive: true });
    console.log(`Created directory: ${configDir}`);
  } catch (err) {
    console.error(`Error creating directory ${configDir}:`, err);
    // Depending on requirements, you might want to exit here if the dir is critical
  }
}

// Initialize data file if it doesn't exist
if (!fs.existsSync(DATA_FILE)) {
  fs.writeFileSync(DATA_FILE, JSON.stringify({
    students: [],
    classes: [],
    books: [],
    genres: [],
    settings: {
      readingStatusSettings: {
        recentlyReadDays: 7,
        needsAttentionDays: 14
      }
    }
  }), 'utf8');
}

// Middleware
// app.use(cors()); // CORS not needed when served from the same origin
app.use(bodyParser.json());

// Serve static files from the React build directory
app.use(express.static(path.join(__dirname, '..', 'build')));

// Helper function to read data
const readData = () => {
  try {
    const data = fs.readFileSync(DATA_FILE, 'utf8');
    const parsedData = JSON.parse(data);

    // Ensure all required data structures exist
    if (!parsedData.students) parsedData.students = [];
    if (!parsedData.classes) parsedData.classes = [];
    if (!parsedData.books) parsedData.books = [];
    if (!parsedData.genres) parsedData.genres = [];
    if (!parsedData.settings) {
      parsedData.settings = {
        readingStatusSettings: {
          recentlyReadDays: 7,
          needsAttentionDays: 14
        }
      };
    }

    return parsedData;
  } catch (error) {
    console.error('Error reading data file:', error);
    return {
      students: [],
      classes: [],
      books: [],
      genres: [],
      settings: {
        readingStatusSettings: {
          recentlyReadDays: 7,
          needsAttentionDays: 14
        }
      }
    };
  }
};

// Helper function to write data
const writeData = (data) => {
  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing data file:', error);
    return false;
  }
};

// Routes
app.get('/api/students', (req, res) => {
  const data = readData();
  res.json(data.students);
});

app.post('/api/students', (req, res) => {
  const data = readData();
  const newStudent = req.body;
  
  data.students.push(newStudent);
  
  if (writeData(data)) {
    res.status(201).json(newStudent);
  } else {
    res.status(500).json({ error: 'Failed to save student' });
  }
});

app.put('/api/students/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;
  const updatedStudent = req.body;
  
  const index = data.students.findIndex(student => student.id === id);
  
  if (index === -1) {
    return res.status(404).json({ error: 'Student not found' });
  }
  
  data.students[index] = updatedStudent;
  
  if (writeData(data)) {
    res.json(updatedStudent);
  } else {
    res.status(500).json({ error: 'Failed to update student' });
  }
});

app.delete('/api/students/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;
  
  const initialLength = data.students.length;
  data.students = data.students.filter(student => student.id !== id);
  
  if (data.students.length === initialLength) {
    return res.status(404).json({ error: 'Student not found' });
  }
  
  if (writeData(data)) {
    res.json({ message: 'Student deleted successfully' });
  } else {
    res.status(500).json({ error: 'Failed to delete student' });
  }
});

// Bulk operations
app.post('/api/students/bulk', (req, res) => {
  const data = readData();
  const newStudents = req.body;
  
  data.students = [...data.students, ...newStudents];
  
  if (writeData(data)) {
    res.status(201).json(newStudents);
  } else {
    res.status(500).json({ error: 'Failed to save students' });
  }
});

// Get all data (for import/export)
app.get('/api/data', (req, res) => {
  const data = readData();
  res.json(data);
});

// Replace all data (for import/export)
app.post('/api/data', (req, res) => {
  const newData = req.body;

  if (writeData(newData)) {
    res.json({ message: 'Data imported successfully', count: newData.students.length });
  } else {
    res.status(500).json({ error: 'Failed to import data' });
  }
});

// Get JSON content (for JSON editor)
app.get('/api/data/json', (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const DATA_FILE = path.join(__dirname, '..', 'config', 'app_data.json');

    if (!fs.existsSync(DATA_FILE)) {
      return res.status(404).json({ error: 'JSON file not found' });
    }

    const content = fs.readFileSync(DATA_FILE, 'utf8');
    res.setHeader('Content-Type', 'application/json');
    res.send(content);
  } catch (error) {
    console.error('Error reading JSON file:', error);
    res.status(500).json({ error: 'Failed to read JSON file' });
  }
});

// Save JSON content (for JSON editor)
app.post('/api/data/save-json', (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'No content provided' });
    }

    // Validate JSON
    JSON.parse(content);

    // Write to file
    const fs = require('fs');
    const path = require('path');
    const DATA_FILE = path.join(__dirname, '..', 'config', 'app_data.json');

    fs.writeFileSync(DATA_FILE, content, 'utf8');

    res.json({ success: true, message: 'JSON saved successfully' });
  } catch (error) {
    console.error('Error saving JSON:', error);
    res.status(400).json({ success: false, message: error.message });
  }
});

// Settings endpoints
app.get('/api/settings', (req, res) => {
  const data = readData();
  
  // If settings don't exist yet, initialize with defaults
  if (!data.settings) {
    data.settings = {
      readingStatusSettings: {
        recentlyReadDays: 7,
        needsAttentionDays: 14
      }
    };
    writeData(data);
  }
  
  res.json(data.settings);
});

app.post('/api/settings', (req, res) => {
  const data = readData();
  const newSettings = req.body;

  // Initialize settings object if it doesn't exist
  if (!data.settings) {
    data.settings = {};
  }

  // Update settings with new values
  data.settings = { ...data.settings, ...newSettings };

  if (writeData(data)) {
    res.json(data.settings);
  } else {
    res.status(500).json({ error: 'Failed to save settings' });
  }
});

// Analytics endpoints
app.post('/api/analytics/event', (req, res) => {
  // Log analytics event (you can extend this to store in database or send to external service)
  const eventData = req.body;
  console.log('Analytics event:', eventData);

  // Return success response
  res.json({ success: true, message: 'Event logged successfully' });
});

app.post('/api/analytics/track/page_view', (req, res) => {
  // Log page view tracking
  const pageViewData = req.body;
  console.log('Page view tracked:', pageViewData);

  // Return success response
  res.json({ success: true, message: 'Page view tracked successfully' });
});

// Classes endpoints
app.get('/api/classes', (req, res) => {
  const data = readData();

  // Initialize classes array if it doesn't exist
  if (!data.classes) {
    data.classes = [];
    writeData(data);
  }

  res.json(data.classes);
});

app.post('/api/classes', (req, res) => {
  const data = readData();
  const newClass = req.body;

  // Initialize classes array if it doesn't exist
  if (!data.classes) {
    data.classes = [];
  }

  // Add default disabled field if not provided
  if (newClass.disabled === undefined) {
    newClass.disabled = false;
  }

  data.classes.push(newClass);

  if (writeData(data)) {
    res.status(201).json(newClass);
  } else {
    res.status(500).json({ error: 'Failed to save class' });
  }
});

app.put('/api/classes/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;
  const updatedClass = req.body;

  // Initialize classes array if it doesn't exist
  if (!data.classes) {
    data.classes = [];
  }

  const index = data.classes.findIndex(cls => cls.id === id);

  if (index === -1) {
    return res.status(404).json({ error: 'Class not found' });
  }

  // Preserve the id and update other fields
  data.classes[index] = { ...updatedClass, id };

  if (writeData(data)) {
    res.json(data.classes[index]);
  } else {
    res.status(500).json({ error: 'Failed to update class' });
  }
});

app.delete('/api/classes/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;

  // Initialize classes array if it doesn't exist
  if (!data.classes) {
    data.classes = [];
  }

  const initialLength = data.classes.length;
  data.classes = data.classes.filter(cls => cls.id !== id);

  if (data.classes.length === initialLength) {
    return res.status(404).json({ error: 'Class not found' });
  }

  if (writeData(data)) {
    res.json({ message: 'Class deleted successfully' });
  } else {
    res.status(500).json({ error: 'Failed to delete class' });
  }
});

// Books endpoints
app.get('/api/books', (req, res) => {
  const data = readData();

  // Initialize books array if it doesn't exist
  if (!data.books) {
    data.books = [];
  }

  res.json(data.books);
});

app.post('/api/books', (req, res) => {
  const data = readData();
  const newBook = req.body;

  // Initialize books array if it doesn't exist
  if (!data.books) {
    data.books = [];
  }

  data.books.push(newBook);

  if (writeData(data)) {
    res.status(201).json(newBook);
  } else {
    res.status(500).json({ error: 'Failed to save book' });
  }
});

app.put('/api/books/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;
  const updatedBook = req.body;

  // Initialize books array if it doesn't exist
  if (!data.books) {
    data.books = [];
  }

  const index = data.books.findIndex(book => book.id === id);

  if (index === -1) {
    return res.status(404).json({ error: 'Book not found' });
  }

  // Preserve the id and update other fields
  data.books[index] = { ...updatedBook, id };

  if (writeData(data)) {
    res.json(data.books[index]);
  } else {
    res.status(500).json({ error: 'Failed to update book' });
  }
});

app.delete('/api/books/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;

  // Initialize books array if it doesn't exist
  if (!data.books) {
    data.books = [];
  }

  const initialLength = data.books.length;
  data.books = data.books.filter(book => book.id !== id);

  if (data.books.length === initialLength) {
    return res.status(404).json({ error: 'Book not found' });
  }

  if (writeData(data)) {
    res.json({ message: 'Book deleted successfully' });
  } else {
    res.status(500).json({ error: 'Failed to delete book' });
  }
});

// Genres endpoints
app.get('/api/genres', (req, res) => {
  const data = readData();

  // Initialize genres array if it doesn't exist
  if (!data.genres) {
    data.genres = [];
  }

  res.json(data.genres);
});

app.post('/api/genres', (req, res) => {
  const data = readData();
  const newGenre = req.body;

  // Initialize genres array if it doesn't exist
  if (!data.genres) {
    data.genres = [];
  }

  data.genres.push(newGenre);

  if (writeData(data)) {
    res.status(201).json(newGenre);
  } else {
    res.status(500).json({ error: 'Failed to save genre' });
  }
});

app.put('/api/genres/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;
  const updatedGenre = req.body;

  // Initialize genres array if it doesn't exist
  if (!data.genres) {
    data.genres = [];
  }

  const index = data.genres.findIndex(genre => genre.id === id);

  if (index === -1) {
    return res.status(404).json({ error: 'Genre not found' });
  }

  // Preserve the id and update other fields
  data.genres[index] = { ...updatedGenre, id };

  if (writeData(data)) {
    res.json(data.genres[index]);
  } else {
    res.status(500).json({ error: 'Failed to update genre' });
  }
});

app.delete('/api/genres/:id', (req, res) => {
  const data = readData();
  const { id } = req.params;

  // Initialize genres array if it doesn't exist
  if (!data.genres) {
    data.genres = [];
  }

  const initialLength = data.genres.length;
  data.genres = data.genres.filter(genre => genre.id !== id);

  if (data.genres.length === initialLength) {
    return res.status(404).json({ error: 'Genre not found' });
  }

  if (writeData(data)) {
    res.json({ message: 'Genre deleted successfully' });
  } else {
    res.status(500).json({ error: 'Failed to delete genre' });
  }
});

// Enhanced AI-powered book recommendations endpoint
app.get('/api/books/recommendations', async (req, res) => {
  try {
    const data = readData();
    const { studentId } = req.query;

    if (!studentId) {
      return res.status(400).json({ error: 'studentId query parameter is required' });
    }

    const student = data.students.find(s => s.id === studentId);
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get student's class info to determine school year
    const studentClass = data.classes.find(c => c.id === student.classId);
    const schoolYear = studentClass ? studentClass.name : 'Unknown';

    // Get books that student has already read (for exclusion)
    const readBooks = [];
    if (student.readingSessions && student.readingSessions.length > 0) {
      student.readingSessions.forEach(session => {
        if (session.bookId) {
          // Try to find the book in the database
          const bookInDb = data.books.find(b => b.id === session.bookId);
          if (bookInDb) {
            readBooks.push(`${bookInDb.title} by ${bookInDb.author || 'Unknown Author'}`);
          } else if (session.bookTitle) {
            // Use session data if available
            readBooks.push(`${session.bookTitle} by ${session.author || 'Unknown Author'}`);
          }
        }
      });
    }

    // Collect genre information
    const genresMap = {};
    data.genres.forEach(genre => {
      genresMap[genre.id] = genre.name;
    });

    // Collect student's available books (for context)
    const availableBooks = data.books.map(book => {
      const genreNames = book.genreIds ? book.genreIds.map(id => genresMap[id]).filter(Boolean) : [];
      return `${book.title} by ${book.author || 'Unknown'} - Genres: ${genreNames.join(', ') || 'Not specified'}`;
    });

    // Gather student's reading preferences
    const preferences = student.preferences || {};
    const favoriteGenres = preferences.favoriteGenreIds ?
      preferences.favoriteGenreIds.map(id => genresMap[id]).filter(Boolean) : [];
    const likes = preferences.likes || [];
    const dislikes = preferences.dislikes || [];

    // Enhanced AI prompt that can generate books from external knowledge
    const readBooksText = readBooks.length > 0 ?
      `The student has read: ${readBooks.join('; ')}` :
      'The student has not read any books yet.';

    const availableBooksText = availableBooks.length > 0 ?
      `\n\nBooks available in library: ${availableBooks.join('; ')}` :
      '';

    const prompt = `You are a children's librarian expert with extensive knowledge of children's literature. Based on the student's information below, recommend 8-12 age-appropriate books FROM YOUR KNOWLEDGE BASE.

Student Details:
- Name: ${student.name}
- School Year: ${schoolYear}
- Reading Level: ${student.readingLevel || 'Not specified'}
${favoriteGenres.length > 0 ? `- Favorite Genres: ${favoriteGenres.join(', ')}` : '- Favorite Genres: No specific preferences'}
${likes.length > 0 ? `- Likes: ${likes.join(', ')}` : '- Likes: Not specified'}
${dislikes.length > 0 ? `- Dislikes: ${dislikes.join(', ')}` : '- Dislikes: Not specified'}

${readBooksText}${availableBooksText}

**Guidelines:**
- Recommend from any age-appropriate books (draw from your complete knowledge of children's literature)
- DO NOT limit yourself to only library books - recommend excellent books from the broader children’s literature canon
- Consider the student as having access to ANY age-appropriate book, not just those in the library
- Balance variety with the student's preferences and interests
- Include classic children's literature, award-winning books, and modern favorites
- Take into account developmental stage and school year
- Consider books that build on reading skills and introduce new concepts
- Provide diversity in genres, formats, and time periods
- Include both fiction and non-fiction suggestions

**Return Format:**
Return exactly a JSON array in this format, with no additional text before or after:

[
  {
    "title": "Book Title",
    "author": "Author Name",
    "genre": "Primary Genre",
    "ageRange": "Age range (e.g., 8-12)",
    "reason": "Brief reason why this book was recommended for this student specifically"
  },
  {
    "title": "Another Book Title",
    "author": "Another Author",
    "genre": "Another Genre",
    "ageRange": "Age range (e.g., 7-11)",
    "reason": "Another specific recommendation reason"
  }
]

Recommendations should be 8-12 excellent books that stimulate the student's interests and development.`;

    console.log('Generating AI recommendations for student:', student.name);

    // Initialize Anthropic client
    const anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    // Make API call to Claude
    const response = await anthropic.messages.create({
      model: 'claude-sonnet-4-20250514',
      max_tokens: 3000,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
    });

    // Parse the AI response
    const recommendationText = response.content[0].text;
    console.log('AI recommendation response:', recommendationText.substring(0, 200) + '...');

    try {
      const recommendations = JSON.parse(recommendationText);
      console.log(`Generated ${recommendations.length} book recommendations for ${student.name}`);

      // Return the recommended books with additional metadata
      res.json({
        recommendations,
        studentName: student.name,
        schoolYear: schoolYear,
        preferredGenres: favoriteGenres,
        booksRead: readBooks.length,
        totalAvailable: data.books.length
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.log('Raw AI response:', recommendationText);

      // Try to extract JSON from the response if there are extra characters
      const jsonMatch = recommendationText.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        try {
          const recommendations = JSON.parse(jsonMatch[0]);
          console.log('Successfully parsed JSON after extraction');

          res.json({
            recommendations,
            studentName: student.name,
            schoolYear: schoolYear,
            preferredGenres: favoriteGenres,
            note: 'Recommendations extracted from AI response'
          });
        } catch (extractionError) {
          console.error('JSON extraction also failed:', extractionError);
          res.status(500).json({
            error: 'Unable to parse AI recommendations',
            note: 'The AI provided recommendations in an unexpected format.'
          });
        }
      } else {
        // Fallback to basic recommendations if all parsing fails
        const fallbackRecommendations = [
          {
            title: "Charlotte's Web",
            author: "E.B. White",
            genre: "Animal Fiction",
            ageRange: "7-12",
            reason: "A timeless classic about friendship and adventure"
          },
          {
            title: "The BFG",
            author: "Roald Dahl",
            genre: "Fantasy",
            ageRange: "7-11",
            reason: "Imaginative story that sparks creativity and humor"
          },
          {
            title: "Hatchet",
            author: "Gary Paulsen",
            genre: "Survival Adventure",
            ageRange: "9-14",
            reason: "Thrilling outdoor adventure promoting resilience"
          },
          {
            title: "Wonder",
            author: "R.J. Palacio",
            genre: "Realistic Fiction",
            ageRange: "8-12",
            reason: "Heartwarming story about kindness and understanding differences"
          }
        ];

        res.json({
          recommendations: fallbackRecommendations,
          studentName: student.name,
          schoolYear: schoolYear,
          preferredGenres: favoriteGenres,
          note: 'AI recommendation parsing failed, showing curated classic selections'
        });
      }
    }

  } catch (error) {
    console.error('Error generating AI recommendations:', error);

    // Final emergency fallback with well-known children's books
    const emergencyRecommendations = [
      {
        title: "The Very Hungry Caterpillar",
        author: "Eric Carle",
        genre: "Picture Book",
        ageRange: "2-8",
        reason: "Excellent early reader with beautiful illustrations"
      },
      {
        title: "Green Eggs and Ham",
        author: "Dr. Seuss",
        genre: "Nonsense Poetry",
        ageRange: "3-7",
        reason: "Fun rhyming book that encourages reading aloud"
      },
      {
        title: "Where the Wild Things Are",
        author: "Maurice Sendak",
        genre: "Picture Book",
        ageRange: "4-8",
        reason: "Wildly imaginative story for imaginative minds"
      },
      {
        title: "Brown Bear, Brown Bear, What Do You See?",
        author: "Bill Martin Jr. and Eric Carle",
        genre: "Picture Book",
        ageRange: "2-6",
        reason: "Predictable text perfect for early learning"
      }
    ];

    res.status(500).json({
      error: 'AI recommendation service temporarily unavailable',
      recommendations: emergencyRecommendations,
      note: 'Providing classic early reader recommendations as backup'
    });
  }
});

// Fallback for client-side routing (serves index.html for non-API routes)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '..', 'build', 'index.html'));
});


// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Serving static files from: ${path.join(__dirname, '..', 'build')}`);
  console.log(`Using data file: ${DATA_FILE}`);
});