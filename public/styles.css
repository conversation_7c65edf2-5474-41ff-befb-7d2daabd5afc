/* Base body and background - lighter, neutral, mobile-first */
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(180deg, #f8f6f4 0%, #f0edea 100%);
  color: #0b3c49;
  min-height: 100vh;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
}

/* Subtle decorative layers (low opacity for performance) */
body::before {
  content: '';
  position: fixed;
  inset: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(99,102,241,0.02), transparent 25%),
    radial-gradient(circle at 90% 80%, rgba(245,158,11,0.02), transparent 25%);
  z-index: -1;
  pointer-events: none;
}

/* Utility container to center content and provide responsive max width */
.app-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
  box-sizing: border-box;
}

/* Lightweight card fallback for non-MUI elements */
.card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(11, 60, 73, 0.06);
  border: 1px solid rgba(11,60,73,0.04);
  padding: 12px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: transform 120ms ease, box-shadow 120ms ease, opacity 120ms;
}

.btn:active { transform: translateY(1px); }

.btn-primary {
  background: linear-gradient(90deg, #0b3c49 0%, #062832 100%);
  color: #fff;
  box-shadow: 0 6px 18px rgba(11,60,73,0.12);
}

.btn-outline {
  background: transparent;
  border: 1px solid rgba(11,60,73,0.08);
  color: #0b3c49;
}

/* Responsive grid helpers */
.row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.col {
  flex: 1 1 0;
  min-width: 0;
}

/* Small card media (avatars/icons) */
.card-media {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg,#e8f4f0,#f0f8f5);
}

/* Make overflowing content scroll nicely on touch devices */
* {
  -webkit-overflow-scrolling: touch;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(11, 60, 73, 0.12);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(11, 60, 73, 0.18);
}

/* Mobile-specific adjustments */
@media (max-width: 600px) {
  .app-container { padding: 12px; }
  .card { border-radius: 10px; padding: 14px; }
  .card-media { width: 44px; height: 44px; border-radius: 8px; }
  .btn { padding: 12px 14px; border-radius: 10px; min-height: 44px; font-size: 1rem; }

  /* Ensure bottom safe-area is respected on iOS */
  body { padding-bottom: calc(env(safe-area-inset-bottom) + 8px); padding-top: env(safe-area-inset-top); }

  /* Make toolbars and tab controls easier to tap */
  .MuiTabs-scrollButtons { opacity: 0.95 !important; min-width: 48px !important; }
  .MuiTabs-flexContainer { display: flex !important; flex-wrap: nowrap !important; }
}

/* Accessibility: visible focus rings */
*:focus-visible {
  outline: 3px solid rgba(11,60,73,0.22);
  outline-offset: 3px;
  border-radius: 6px;
}

/* Small helpers for truncation and ellipsis */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Visually-hidden helper for screen readers */
.sr-only {
  position: absolute !important;
  height: 1px; width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap;
}

/* Ensure form elements and interactive controls have sufficient hit area */
input, button, select, textarea {
  touch-action: manipulation;
}

/* Force ALL avatars to be square (Material-UI override) */
.MuiAvatar-root {
  border-radius: 8px !important;
}