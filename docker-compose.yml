version: '3.8'

services:
  # Frontend service
  kids-reading-manager:
    build:
      context: .
      dockerfile: Dockerfile
    image: kids-reading-manager:latest
    container_name: kids-reading-manager
    ports:
      - "8080:3000" # Map host 8080 to container 3000 (Node server port)
    restart: unless-stopped
    volumes:
      # Mount the host config directory into the container
      - ./config:/config
    # No network needed for single service
    # No depends_on needed

# Removed api-server service
# Removed networks definition
# Removed volumes definition