<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .icon {
            width: 64px;
            height: 64px;
            margin: 20px;
            border: 1px solid #ccc;
            padding: 10px;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        code {
            background-color: #eee;
            padding: 2px 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Book Icon for Favicon</h1>
        
        <div>
            <h2>Book Icon (64x64)</h2>
            <svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#6b4d57">
                <path d="M21 4H3c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM3 19V6h8v13H3zm18 0h-8V6h8v13zm-7-9.5h6V11h-6zm0 2.5h6v1.5h-6zm0 2.5h6V16h-6z"/>
            </svg>
        </div>
        
        <div class="instructions">
            <h2>Instructions to Save as Favicon</h2>
            <ol>
                <li>Right-click on the book icon above</li>
                <li>Select "Save Image As..."</li>
                <li>Save it as <code>favicon.ico</code> in the <code>public</code> directory of your project</li>
            </ol>
            <p>Alternatively, you can use an online favicon generator tool:</p>
            <ol>
                <li>Take a screenshot of the icon above</li>
                <li>Go to a site like <a href="https://favicon.io/favicon-converter/" target="_blank">favicon.io</a></li>
                <li>Upload the screenshot and generate a favicon</li>
                <li>Download and save it as <code>favicon.ico</code> in the <code>public</code> directory</li>
            </ol>
        </div>
    </div>
</body>
</html>